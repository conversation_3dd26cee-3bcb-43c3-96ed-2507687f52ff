.form-group {
  margin-bottom: 1.25rem; /* Slightly increased for better spacing */
}
label {
  margin-bottom: 0.5rem; /* Controls space between label and input */
}
.bold-label {
  font-weight: 500 !important; /* Bold when readonly */
}

.form-control {
  border: 1px solid var(--color-border, #ced4da); /* Clear outline using theme variable */
  border-radius: 6px; /* Slightly tighter radius for modern look */
  background-color: var(--color-background, #ffffff); /* Clean white background */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* Subtle shadow for depth */
  color: var(--color-text-dark, #333333); /* Readable text color */
  padding: 0.75rem 1rem; /* Comfortable padding */
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease; /* Smooth transitions */
}

.form-control:focus {
  outline: none;
  border-color: var(--color-poppy-blue, #0a54c4); /* Highlight on focus */
  box-shadow: 0 0 0 3px rgba(6, 41, 236, 0.2); /* Subtle glow effect */
}

.is-invalid {
  border-color: var(--color-poppy-red, #dc3545); /* Error state border */
}

.is-invalid:focus {
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2); /* Error state focus glow */
}

.invalid-input {
  opacity: 0; /* Hidden by default */
  color: var(--color-poppy-red, #dc3545);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  transform: translateY(-4px); /* Slight upward shift for animation */
  transition: opacity 0.2s ease, transform 0.2s ease; /* Smooth fade-in */
}

.invalid-input.show-error {
  opacity: 1; /* Visible on hover */
  transform: translateY(0); /* Slide into place */
}

.red-font {
  color: var(--color-poppy-red, #dc3545); /* Mandatory field indicator */
}

.show-data {
  margin-bottom: 0.8rem;
  color: var(--color-text-dark, #333333);
  font-size: 1rem;
}

.x-error-msg-text {
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Ensure Material components align with theme */
:host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
  background-color: var(--color-background, #ffffff); /* White background for Material inputs */
  border-radius: 6px;
}

:host ::ng-deep .mat-form-field-underline {
  display: none; /* Remove Material underline for consistency */
}

:host ::ng-deep .mat-form-field-infix {
  padding: 0.5rem 0;
}

