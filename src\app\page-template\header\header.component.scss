.app-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: var(--color-secondary);
    box-shadow: 0 2px 10px var(--shadow-light);
    z-index: 1000;
    transition: all 0.3s ease;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.header-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
    position: relative;
}

.brand {
    display: flex;
    align-items: center;
    flex: 0 0 auto;
    z-index: 1001;
    // Desktop: logo on the left side
    order: 1;
}

.logo {
    text-decoration: none;
    display: flex;
    align-items: center;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-tertiary);
    letter-spacing: -0.5px;
    background: linear-gradient(to right, var(--color-tertiary), var(--color-tertiary-shade));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    white-space: nowrap;
}

.nav-menu {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: center;
    order: 2;
}

.nav-header {
    display: none;
}

.nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin: 0 5px;
}

.nav-link {
    position: relative;
    display: block;
    padding: 10px 15px;
    color: var(--color-nav-link-text);
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    transition: color 0.3s;
}

.nav-link:hover,
.nav-link.active {
    color: var(--color-nav-link-hover);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background-color: var(--color-tertiary);
    transition: width 0.3s;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 80%;
}

.header-actions {
    display: flex;
    align-items: center;
    flex: 0 0 auto;
    order: 3;
}

.action-button {
    display: inline-block;
    padding: 8px 20px;
    background-color: var(--color-tertiary);
    color: var(--color-btn-primary-text);
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s;
    white-space: nowrap;
}

.action-button:hover {
    background-color: var(--color-tertiary-shade);
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    z-index: 1001;
    flex-shrink: 0;
    color: var(--color-tertiary);
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.nav-toggle {
    order: 1;
}

.sidebar-toggle {
    order: 3;
}

.bar {
    display: block;
    width: 22px;
    height: 2px;
    margin: 3px 0;
    background-color: var(--color-tertiary);
    transition: all 0.3s;
    border-radius: 1px;
}

.menu-toggle.active .bar:nth-child(1) {
    transform: translateY(5px) rotate(45deg);
}

.menu-toggle.active .bar:nth-child(2) {
    opacity: 0;
}

.menu-toggle.active .bar:nth-child(3) {
    transform: translateY(-5px) rotate(-45deg);
}

.close-nav {
    background: none;
    border: none;
    color: var(--color-tertiary);
    cursor: pointer;
    padding: 8px;
}

.mobile-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--color-overlay-medium);
    z-index: 999;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mobile-overlay.active {
    display: block;
    opacity: 1;
}

.mobile-nav-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--color-overlay-medium);
    z-index: 999;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mobile-nav-overlay.active {
    display: block;
    opacity: 1;
}

.mobile-sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--color-overlay-medium);
    z-index: 999;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mobile-sidebar-overlay.active {
    display: block;
    opacity: 1;
}

@media (max-width: 768px) {
    body {
        padding-top: 60px;
    }

    .container {
        padding: 0 15px;
    }

    .header-wrapper {
        height: 60px;
    }

    .brand {
        // Mobile: logo in the center
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        order: 2;
    }

    .logo-text {
        font-size: 1.3rem;
    }

    .menu-toggle {
        display: flex;
    }
    
    .nav-toggle {
        // Left side - for sidebar
        order: 1;
    }
    
    .sidebar-toggle {
        // Right side - for nav menu
        order: 3;
    }

    .nav-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid var(--color-border-light);
        font-weight: 600;
        color: var(--color-tertiary);
    }

    .nav-menu {
        position: fixed;
        top: 0;
        right: -100%;  // Nav menu slides from right
        width: 280px;
        max-width: 80vw;
        height: 100vh;
        background-color: var(--color-primary);
        box-shadow: -2px 0 10px var(--shadow-light);  // Shadow on the left side
        transition: right 0.3s ease;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        padding: 0 0 20px;
        z-index: 1000;
        overflow-y: auto;
        order: 2;
    }

    .nav-menu.open {
        right: 0;
    }

    .nav-list {
        flex-direction: column;
        width: 100%;
        padding: 20px;
    }

    .nav-item {
        margin: 8px 0;
        width: 100%;
    }

    .nav-link {
        padding: 12px 0;
        width: 100%;
        border-bottom: 1px solid var(--color-border-light);
    }

    .nav-link::after {
        display: none;
    }

    .header-actions {
        display: none;
    }

    .mobile-header-actions {
        display: flex;
        flex-direction: column;
        width: 100%;
        margin-top: 20px;
        padding: 0 20px 20px;
        border-top: 1px solid var(--color-border-light);
    }

    .mobile-header-actions .action-button {
        width: 100%;
        text-align: center;
        margin-bottom: 10px;
        padding: 12px 20px;
    }

    .mobile-header-actions .action-button:last-child {
        margin-bottom: 0;
    }

    body.menu-open {
        overflow: hidden;
    }

    body.sidebar-open {
        overflow: hidden;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }

    .logo-text {
        font-size: 1.2rem;
    }

    .nav-menu {
        width: 100%;
        max-width: 100%;
    }
}