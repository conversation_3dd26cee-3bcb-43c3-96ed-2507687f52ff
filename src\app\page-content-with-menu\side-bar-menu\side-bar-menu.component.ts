import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';

export interface MenuItem {
  id: string;
  title: string;
  icon: string;
  route?: string;
  roles: string[];
  children?: MenuItem[];
}

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './side-bar-menu.component.html',
  styleUrls: ['./side-bar-menu.component.scss']
})
export class SidebarComponent implements OnInit {
  @Input() isCollapsed = false;
  @Input() isVisible = false;
  @Input() userRole: string = 'admin';
  @Output() onToggle = new EventEmitter<void>();
  @Output() onNavigate = new EventEmitter<void>();

  expandedSubmenu: string | null = null;

  menuItems: MenuItem[] = [
    {
      id: 'homepage',
      title: 'Home',
      icon: 'home',
      route: '/',
      roles: ['admin', 'user', 'moderator', 'guest'],
    },
    {
      id: 'dashboard',
      title: 'Dashboard',
      icon: 'dashboard',
      route: '/dashboard',
      roles: ['admin', 'user', 'moderator', 'guest'],
    },
    {
      id: 'external-tracking',
      title: 'External Services',
      icon: 'track_changes',
      route: '/dashboard/external-services-tracking',
      roles: ['admin', 'user'],
    },
    {
      id: 'example-form',
      title: 'Dynamic Form',
      icon: 'dynamic_form',
      route: '/dashboard/example-form',
      roles: ['admin'],
    },
    {
      id: 'reports',
      title: 'Reports',
      icon: 'assessment',
      roles: ['admin', 'moderator'],
      children: [
        {
          id: 'sales-report',
          title: 'Usage Report',
          icon: 'trending_up',
          route: '/reports/sales',
          roles: ['admin', 'moderator'],

        },
        {
          id: 'user-report',
          title: 'Engagement',
          icon: 'people_outline',
          route: '/reports/users',
          roles: ['admin'],
        },
      ],
    },
  ];

  constructor(private router: Router) { }

  ngOnInit(): void { }

  toggleSubmenu(submenuId: string): void {
    if (this.isCollapsed) {
      this.onToggle.emit();
      setTimeout(() => {
        this.expandedSubmenu = submenuId;
      }, 300);
    } else {
      this.expandedSubmenu =
        this.expandedSubmenu === submenuId ? null : submenuId;
    }
  }

  navigate(path: string | undefined): void {
    if (!path) return;
    this.router.navigate([path]);
    this.onNavigate.emit();
  }


  canAccess(item: MenuItem): boolean {
    return item.roles.includes(this.userRole);
  }
}
