.form-field-container {
  margin-bottom: 1.5rem;
  
  .form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--color-text-dark);
    font-size: 0.875rem;
    
    .required-indicator {
      color: var(--color-poppy-red);
      margin-left: 0.25rem;
    }
  }
  
  .form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--color-form-input-border);
    border-radius: 0.375rem;
    font-size: 1rem;
    color: var(--color-form-input-text);
    background-color: var(--color-form-input-bg);
    transition: all 0.2s ease-in-out;
    
    &::placeholder {
      color: var(--color-form-input-placeholder);
    }
    
    &:hover {
      border-color: var(--color-form-input-border-hover);
    }
    
    &:focus {
      outline: none;
      border-color: var(--color-form-input-border-focus);
      box-shadow: 0 0 0 3px var(--shadow-focus);
    }
    
    &.error {
      border-color: var(--color-form-input-error);
      background-color: var(--color-notification-error-bg);
      
      &:focus {
        box-shadow: 0 0 0 3px hsla(348, 99%, 33%, 0.15);
      }
    }
    
    &.success {
      border-color: var(--color-form-input-success);
      background-color: var(--color-notification-success-bg);
      
      &:focus {
        box-shadow: 0 0 0 3px hsla(142, 76%, 45%, 0.15);
      }
    }
    
    &:disabled {
      background-color: var(--color-utility-disabled);
      color: var(--color-utility-disabled-text);
      cursor: not-allowed;
    }
  }
  
  .error-messages {
    margin-top: 0.5rem;
    
    .error-message {
      display: block;
      color: var(--color-notification-error-text);
      font-size: 0.875rem;
      margin-bottom: 0.25rem;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}