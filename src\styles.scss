@use '@angular/material' as mat;
@use "../theme-colors" as theme-colors;
  @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap');
@import url('https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/dist/feather.min.css');

:root {
  @include mat.datepicker-overrides((
    calendar-body-label-text-color: var(--color-poppy-yellow),
    calendar-container-background-color: var(--color-tertiary-shade),
  ));

    /* Main Theme Colors - 5 Colors Only */
  --color-primary: hsl(229, 85%, 98%);            /* #fefae0 */
  --color-secondary: hsl(0, 0%, 100%);          /* #ccd5ae */
  --color-secondary-shade: hsl(76, 45%, 90%);    /* #e9edc9 */
  --color-tertiary: red;           /* #d4a373 */
  --color-tertiary-shade: hsl(253, 85%, 93%);     /* #faedcd */
  --color-transparent:transparent;



  /* Text Colors - 3 Black Shades */
  --color-text-dark: hsl(0, 0%, 10%);            /* #1a1a1a */
  --color-text-medium: hsl(0, 0%, 29%);          /* #4a4a4a */
  --color-text-light: hsl(0, 0%, 48%);           /* #7a7a7a */
  --color-text-white: hsl(0, 0%, 100%); // White text for dark backgrounds

  /* Supporting Poppy Colors - 5 Colors */
  --color-poppy-red: hsl(0, 100%, 70%);          /* #ff6b6b */
  --color-poppy-orange: hsl(20, 100%, 66%);      /* #ff8e53 */
  --color-poppy-yellow: hsl(47, 100%, 64%);      /* #ffd93d */
  --color-poppy-green: hsl(139, 55%, 61%);       /* #6bcf7f */
  --color-poppy-blue: hsl(174, 67%, 60%);        /* #4ecdc4 */


  /*Link Colors */
  --color-text-link: ver(--color-tertiary);
  --color-text-link-hover: var(--color-tertiary-shade);
  /* Button Colors */
  --color-btn-primary-text: var(--color-text-dark);

  /* Secondary Button (White with Red border) */
  --color-btn-secondary-text: var(--color-text-dark);

  // $$$$$$$$$$$$$$$$$$$$$$$@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@####################################################

  // /* Outline Button */
  // --color-btn-outline-active: hsl(348, 100%, 95%);
  // --color-btn-outline-text: hsl(348, 99%, 33%);
  // --color-btn-outline-border: hsl(348, 99%, 33%);

  // /* Ghost Button */
  // --color-btn-ghost-bg: transparent;
  // --color-btn-ghost-hover: hsl(348, 100%, 98%);
  // --color-btn-ghost-active: hsl(348, 100%, 95%);
  // --color-btn-ghost-text: hsl(348, 99%, 33%);

  // /* Danger Button (Same as primary for consistency) */
  // --color-btn-danger-bg: hsl(348, 99%, 33%);
  // --color-btn-danger-hover: hsl(348, 99%, 25%);
  // --color-btn-danger-active: hsl(348, 99%, 20%);
  // --color-btn-danger-text: hsl(0, 0%, 100%);

  // /* Border Colors */
  // --color-border-light: hsl(0, 0%, 90%); // Light gray borders
  // --color-border-medium: hsl(0, 0%, 80%); // Medium gray borders
  // --color-border-dark: hsl(0, 0%, 70%); // Dark gray borders
  // --color-border-focus: hsl(348, 99%, 33%); // Red focus ring color

  // /* Form Colors */
  // --color-form-input-bg: hsl(0, 0%, 100%);
  // --color-form-input-border: hsl(0, 0%, 85%);
  // --color-form-input-border-hover: hsl(0, 0%, 75%);
  // --color-form-input-border-focus: hsl(348, 99%, 33%);
  // --color-form-input-text: hsl(0, 0%, 13%);
  // --color-form-input-placeholder: hsl(0, 0%, 60%);

  // /* Form Validation States */
  // --color-form-input-success: hsl(142, 76%, 45%);
  // --color-form-input-warning: hsl(45, 100%, 55%);
  // --color-form-input-error: hsl(348, 99%, 33%);

  // /* Navigation Colors */
  // --color-nav-bg: hsl(0, 0%, 100%);
  // --color-nav-border: hsl(0, 0%, 90%);
  // --color-nav-link-text: hsl(0, 0%, 40%);
  // --color-nav-link-hover: hsl(348, 99%, 33%);
  // --color-nav-link-active: hsl(348, 99%, 33%);
  // --color-nav-link-active-bg: hsl(348, 100%, 97%);

  /* Table Colors */
  // --color-table-header-bg: hsl(0, 0%, 98%);
  // --color-table-header-text: hsl(0, 0%, 13%);
  // --color-table-row-even: hsl(0, 0%, 100%);
  // --color-table-row-odd: hsl(0, 0%, 99%);
  // --color-table-row-hover: hsl(348, 100%, 98%);
  // --color-table-border: hsl(0, 0%, 90%);

  // /* Status Colors */
  // --color-status-online: hsl(142, 76%, 45%);
  // --color-status-pending: hsl(45, 100%, 55%);
  // --color-status-offline: hsl(0, 0%, 60%);
  // --color-status-draft: hsl(348, 99%, 33%);

  // /* Shadow Colors */
  // --shadow-light: hsla(0, 0%, 0%, 0.05);
  // --shadow-medium: hsla(0, 0%, 0%, 0.1);
  // --shadow-dark: hsla(0, 0%, 0%, 0.15);
  // --shadow-focus: hsla(348, 99%, 33%, 0.25);

  // /* Overlay Colors */
  // --color-overlay-light: hsla(0, 0%, 0%, 0.3);
  // --color-overlay-medium: hsla(0, 0%, 0%, 0.5);
  // --color-overlay-dark: hsla(0, 0%, 0%, 0.7);

  /* Notification Colors */
  // --color-notification-success-bg: hsl(142, 76%, 95%);
  // --color-notification-success-border: hsl(142, 76%, 45%);
  // --color-notification-success-text: hsl(142, 76%, 30%);

  // --color-notification-warning-bg: hsl(45, 100%, 95%);
  // --color-notification-warning-border: hsl(45, 100%, 55%);
  // --color-notification-warning-text: hsl(45, 100%, 35%);

  // --color-notification-error-bg: hsl(348, 100%, 95%);
  // --color-notification-error-border: hsl(348, 99%, 33%);
  // --color-notification-error-text: hsl(348, 99%, 25%);

  // --color-notification-info-bg: hsl(200, 100%, 95%);
  // --color-notification-info-border: hsl(200, 100%, 50%);
  // --color-notification-info-text: hsl(200, 100%, 35%);

  /* Utility Colors */
  // --color-utility-disabled: hsl(0, 0%, 85%);
  // --color-utility-disabled-text: hsl(0, 0%, 60%);
  // --color-utility-backdrop: hsla(0, 0%, 0%, 0.4);
  // --color-utility-selection: hsla(348, 99%, 33%, 0.2);

  // /* Additional Red Theme Variants */
  // --color-red-50: hsl(348, 100%, 97%);
  // --color-red-100: hsl(348, 100%, 95%);
  // --color-red-200: hsl(348, 96%, 89%);
  // --color-red-300: hsl(348, 94%, 80%);
  // --color-red-400: hsl(348, 91%, 69%);
  // --color-red-500: hsl(348, 85%, 55%);
  // --color-red-600: hsl(348, 99%, 33%); // Your main color
  // --color-red-700: hsl(348, 99%, 25%);
  // --color-red-800: hsl(348, 99%, 20%);
  // --color-red-900: hsl(348, 99%, 15%);

  // /* Gray Scale */
  // --color-gray-50: hsl(0, 0%, 98%);
  // --color-gray-100: hsl(0, 0%, 95%);
  // --color-gray-200: hsl(0, 0%, 90%);
  // --color-gray-300: hsl(0, 0%, 80%);
  // --color-gray-400: hsl(0, 0%, 70%);
  // --color-gray-500: hsl(0, 0%, 60%);
  // --color-gray-600: hsl(0, 0%, 40%);
  // --color-gray-700: hsl(0, 0%, 30%);
  // --color-gray-800: hsl(0, 0%, 20%);
  // --color-gray-900: hsl(0, 0%, 13%);
}

// Theme Color Mappings
$primary-color: var(--color-tertiary);
$secondary-color: var(--color-tertiary);
$accent-color: var(--color-tertiary-shade);
$light-color: var(--color-tertiary-shade);
$dark-color: var(--color-tertiary-shade);

$success-color: var(--color-poppy-green);
$warning-color: var(--color-poppy-yellow);
$danger-color: var(--color-poppy-red);
$info-color: var(--color-poppy-blue);

$text-primary: var(--color-text-dark);
$text-secondary: var(--color-text-medium);
$text-muted: var(--color-text-light);
$text-inverse: var(--color-text-white);
$text-link: var(--color-text-link);
$text-link-hover: var(--color-text-link-hover);

$border-color: var(--color-border-light);
$border-medium: var(--color-border-medium);
$border-focus: var(--color-border-focus);

$btn-primary-bg: var(--color-tertiary);
$btn-primary-hover: var(--color-tertiary-shade);
$btn-primary-active: var(--color-tertiary);
$btn-primary-text: var(--color-btn-primary-text);

$btn-secondary-bg: var(--color-secondary);
$btn-secondary-text: var(--color-btn-secondary-text);
$btn-secondary-border: var(--color-secondary);

$box-shadow: 0 4px 8px var(--shadow-light); // Update if needed

/* ======================
Main Content Container
====================== */
html {
  @include mat.theme((
    color: (
      primary: theme-colors.$primary-palette,
      tertiary: theme-colors.$tertiary-palette,
      theme-type: light,
    ),
    typography: Roboto,
    density: 0
  ));
}
/* ======================
   Main Content Container
====================== */
.main-container {
  margin-left: 240px; /* Default for open sidebar on desktop - matches sidebar width */
  width: calc(100% - 240px);
  min-height: calc(100vh - 70px);
  padding: 30px 40px;
  background-color: var(--color-primary);
  color: var(--color-text-dark);
  transition: all 0.3s ease;
  position: relative;
  overflow-x: hidden;
}

/* Sidebar collapsed on desktop */
.sidebar-collapsed .main-container {
  margin-left: 70px; /* Adjust for collapsed sidebar - matches collapsed sidebar width */
  width: calc(100% - 70px);
}

/* Tablet and Mobile: sidebar becomes overlay, so container takes full width */
@media (max-width: 1024px) {
  .main-container {
    margin-left: 0 !important; /* Important to override any other margin settings */
    width: 100% !important; /* Important to override any other width settings */
    padding: 25px 30px;
  }

  /* When sidebar is visible on mobile, add slight blur/dim effect to main content */
  body.sidebar-mobile-open .main-container {
    filter: blur(1px);
    opacity: 0.8;
  }
}

/* Mobile (large phones) */
@media (max-width: 768px) {
  .main-container {
    padding: 20px 20px;
  }
}

/* Small phones */
@media (max-width: 480px) {
  .main-container {
    padding: 15px 15px;
  }
}


.page-layout {
  position: relative;
  min-height: 100vh;
}

.main-content {
  min-height: 100vh;
  background-color: var(--color-primary, #f8f9fa);

  .content-wrapper {
    padding: 1.5rem;
  }
}

/* Prevent scrolling when sidebar is open */
body.sidebar-open {
  overflow: hidden;
}

@media (max-width: 768px) {
  .main-content .content-wrapper {
    padding: 1rem;
  }
}