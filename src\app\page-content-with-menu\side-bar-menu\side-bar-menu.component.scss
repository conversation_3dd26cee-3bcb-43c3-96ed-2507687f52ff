@import url('https://fonts.googleapis.com/icon?family=Material+Icons');

$sidebar-width-expanded: 280px;
$sidebar-width-collapsed: 70px;
$top-navbar-height: 70px; // Match header height
$transition-speed: 0.3s;
$mobile-breakpoint: 1024px;

/* ======================
   Sidebar Base Styles
====================== */
.sidebar {
  position: fixed;
  top: $top-navbar-height;
  left: 0;
  width: $sidebar-width-expanded;
  height: calc(100vh - #{$top-navbar-height});
  background-color: var(--color-secondary, #ffffff);
  border-right: 1px solid var(--color-border-light, #e9ecef);
  z-index: 1000;
  transition: width $transition-speed ease-in-out, transform $transition-speed ease-in-out;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;

  &::-webkit-scrollbar { width: 6px; }
  &::-webkit-scrollbar-track { background: var(--color-primary, #f8f9fa); }
  &::-webkit-scrollbar-thumb { background: var(--color-border-medium, #ced4da); border-radius: 3px; }
  &::-webkit-scrollbar-thumb:hover { background: var(--color-border-dark, #adb5bd); }

  &.collapsed {
    width: $sidebar-width-collapsed;
  }
}

/* ======================
   Mobile View Behavior
====================== */
@media (max-width: $mobile-breakpoint) {
  .sidebar {
    transform: translateX(-100%);
    width: $sidebar-width-expanded;
    box-shadow: 0 0 25px rgba(0,0,0,0.2);

    &.visible-mobile {
      transform: translateX(0);
    }

    // On mobile, the sidebar is never in a "collapsed" visual state
    &.collapsed {
      width: $sidebar-width-expanded;
    }
  }
}

/* ======================
   Sidebar Header
====================== */
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 16px;
  border-bottom: 1px solid var(--color-border-light, #e9ecef);
  background-color: var(--color-secondary, #ffffff);
  color: white;
}

.sidebar-header .brand-section .brand-logo {
  display: flex;
  align-items: center;
}

.sidebar-header .brand-section .brand-name {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.sidebar-header .toggle-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.sidebar-header .toggle-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-header .toggle-btn .material-icons {
  font-size: 20px;
}

/* ======================
   User Profile
====================== */
.user-profile {
  display: flex;
  align-items: center;
  padding: 15px 16px;
  background-color: #ffe6e8; // Light red background
  border-bottom: 1px solid var(--color-border-light, #e9ecef);
  margin-bottom: 8px;
}

.user-profile .user-info {
  flex: 1;
}

.user-profile .user-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-dark, #212529);
  margin-bottom: 2px;
}

.user-profile .user-role {
  font-size: 12px;
  color: var(--color-text-medium, #6c757d);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.user-profile .user-status .status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--color-status-online, #198754);
}

/* ======================
   Navigation
====================== */
.sidebar-nav {
  flex: 1;
  padding: 0 8px;
}

.sidebar-nav .nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav .nav-item {
  margin-bottom: 4px;
}

.sidebar-nav .nav-link {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: var(--color-text-medium, #6c757d);
  text-decoration: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  white-space: nowrap;
}

.sidebar-nav .nav-link:hover {
  background-color: var(--color-tertiary-shade, #eef5ff);
  color: var(--color-tertiary, #0d6efd);
}

.sidebar-nav .nav-link.active {
  background-color: #c10e21;
  color: white;
}

.sidebar-nav .nav-icon {
  font-size: 20px;
  margin-right: 12px;
  min-width: 24px;
}

.sidebar-nav .nav-text {
  flex: 1;
}

.sidebar-nav .nav-badge {
  background-color: #ff9800;
  color: white;
  border-radius: 10px;
  padding: 2px 8px;
  font-size: 12px;
}

.sidebar-nav .expand-icon {
  font-size: 18px;
  margin-left: 4px;
}

/* ======================
   Submenu
====================== */
.submenu {
  list-style: none;
  padding: 0 0 0 40px;
  margin: 5px 0;
}

.submenu-item {
  margin-bottom: 2px;
}

.submenu-link {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  color: var(--color-text-medium, #6c757d);
  text-decoration: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.submenu-link:hover {
  background-color: var(--color-tertiary-shade, #eef5ff);
  color: var(--color-tertiary, #0d6efd);
}

.submenu-link.active {
  background-color: var(--color-tertiary-shade, #eef5ff);
  color: var(--color-tertiary, #0d6efd);
}

.submenu-icon {
  font-size: 18px;
  margin-right: 10px;
}

.submenu-text {
  flex: 1;
}

/* ======================
   Sidebar Footer
====================== */
.sidebar-footer {
  padding: 16px;
  border-top: 1px solid var(--color-border-light, #e9ecef);
}

.footer-actions {
  display: flex;
  justify-content: space-around;
}

.footer-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 10px;
  color: var(--color-text-medium, #6c757d);
  transition: all 0.2s ease;
}

.footer-btn:hover {
  color: var(--color-tertiary, #0d6efd);
}

.footer-btn .material-icons {
  font-size: 20px;
  margin-bottom: 5px;
}

/* Collapsed footer */
.sidebar-footer.collapsed {
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.footer-btn-collapsed {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 10px;
  margin-bottom: 5px;
  color: var(--color-text-medium, #6c757d);
  transition: all 0.2s ease;
}

.footer-btn-collapsed:hover {
  color: var(--color-tertiary, #0d6efd);
}

.footer-btn-collapsed .material-icons {
  font-size: 20px;
}

/* Animation for collapsed state items */
.sidebar.collapsed .nav-icon {
  margin-right: 0;
}

.sidebar.collapsed .nav-link {
  justify-content: center;
  padding: 12px;
}